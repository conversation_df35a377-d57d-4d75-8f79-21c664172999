#!/usr/bin/env node

const fs = require('fs');

// قراءة الملف الحالي
function loadProducts() {
  try {
    const products = JSON.parse(fs.readFileSync('../professional-products.json', 'utf8'));
    console.log(`📖 تم تحميل ${products.length} منتج`);
    return products;
  } catch (error) {
    console.error('❌ خطأ في قراءة الملف:', error.message);
    return null;
  }
}

// تحويل البوصة إلى سم
function inchesToCm(inches) {
  return Math.round(inches * 2.54 * 10) / 10; // تقريب لرقم عشري واحد
}

// استخراج الحجم المحسن
function extractEnhancedSize(description) {
  const sizeMatch = description.match(/(\d+(?:\.\d+)?)\s*(inches?|cm|سم|بوصة)/i);
  if (sizeMatch) {
    const value = parseFloat(sizeMatch[1]);
    const unit = sizeMatch[2].toLowerCase();
    
    if (unit.includes('inch') || unit.includes('بوصة')) {
      const cmValue = inchesToCm(value);
      return {
        en: `${value}" (${cmValue} cm)`,
        ar: `${value} بوصة (${cmValue} سم)`
      };
    } else {
      const inchValue = Math.round(value / 2.54 * 10) / 10;
      return {
        en: `${inchValue}" (${value} cm)`,
        ar: `${inchValue} بوصة (${value} سم)`
      };
    }
  }
  return null;
}

// إنشاء مميزات فريدة لكل منتج
function generateUniqueFeatures(description, titleAr) {
  const features = {
    en: [],
    ar: []
  };
  
  // مميزات أساسية حسب نوع المنتج
  if (description.toLowerCase().includes('serving bowl') || titleAr.includes('وعاء تقديم')) {
    features.en.push('Perfect for serving salads and soups');
    features.ar.push('مثالي لتقديم السلطات والشوربات');
    
    features.en.push('Ergonomic design for easy handling');
    features.ar.push('تصميم مريح للاستخدام السهل');
  }
  
  if (description.toLowerCase().includes('plate') || titleAr.includes('طبق')) {
    features.en.push('Stackable design saves storage space');
    features.ar.push('تصميم قابل للتكديس يوفر مساحة التخزين');
    
    features.en.push('Elegant presentation for professional dining');
    features.ar.push('عرض أنيق للمطاعم الراقية');
  }
  
  if (description.toLowerCase().includes('tray') || titleAr.includes('صينية')) {
    features.en.push('Large serving capacity for buffets');
    features.ar.push('سعة تقديم كبيرة للبوفيهات');
    
    features.en.push('Non-slip base for stability');
    features.ar.push('قاعدة مانعة للانزلاق للثبات');
  }
  
  // مميزات حسب المادة
  if (description.toLowerCase().includes('melamine')) {
    features.en.push('Lightweight yet durable melamine construction');
    features.ar.push('خفيف الوزن ومتين من الميلامين');
    
    features.en.push('Shatterproof and chip resistant');
    features.ar.push('مقاوم للكسر والتشقق');
  }
  
  // مميزات حسب اللون
  if (description.toLowerCase().includes('black')) {
    features.en.push('Sophisticated black finish');
    features.ar.push('لمسة نهائية سوداء أنيقة');
    
    if (description.toLowerCase().includes('matte')) {
      features.en.push('Matte finish reduces fingerprints');
      features.ar.push('اللمسة المطفية تقلل بصمات الأصابع');
    }
  }
  
  // مميزات حسب الحجم
  const size = extractEnhancedSize(description);
  if (size) {
    const sizeValue = parseFloat(description.match(/(\d+(?:\.\d+)?)/)?.[1] || 0);
    if (sizeValue >= 12) {
      features.en.push('Large size perfect for group serving');
      features.ar.push('حجم كبير مثالي لتقديم المجموعات');
    } else if (sizeValue >= 8) {
      features.en.push('Medium size ideal for individual portions');
      features.ar.push('حجم متوسط مثالي للحصص الفردية');
    } else {
      features.en.push('Compact size for appetizers and sauces');
      features.ar.push('حجم مدمج للمقبلات والصلصات');
    }
  }
  
  // مميزات عامة احترافية
  features.en.push('Commercial grade quality for heavy use');
  features.ar.push('جودة تجارية للاستخدام المكثف');
  
  features.en.push('Easy to clean and maintain');
  features.ar.push('سهل التنظيف والصيانة');
  
  // تحديد العدد إلى 6 مميزات
  return {
    en: features.en.slice(0, 6),
    ar: features.ar.slice(0, 6)
  };
}

// استخراج العنوان من الوصف
function extractTitleFromDescription(description, isArabic = false) {
  if (isArabic) {
    // استخراج العنوان العربي من الوصف
    // البحث عن أنماط مثل "وعاء تقديم" أو "طبق" أو "صينية"
    const patterns = [
      /وعاء تقديم[^،.]*/,
      /طبق[^،.]*/,
      /صينية[^،.]*/,
      /سلطانية[^،.]*/,
      /كوب[^،.]*/,
      /إبريق[^،.]*/,
      /قدر[^،.]*/
    ];

    for (const pattern of patterns) {
      const match = description.match(pattern);
      if (match) {
        let title = match[0].trim();
        // إضافة معلومات إضافية من الوصف
        const sizeMatch = description.match(/(\d+(?:\.\d+)?)\s*(بوصة|سم)/);
        const materialMatch = description.match(/(ميلامين|بورسلين|فولاذ|زجاج)/);
        const colorMatch = description.match(/(أسود|أبيض|شفاف|ملون)/);

        if (materialMatch) title += ` ${materialMatch[1]}`;
        if (sizeMatch) title += ` ${sizeMatch[1]} ${sizeMatch[2]}`;
        if (colorMatch) title += ` ${colorMatch[1]}`;

        return optimizeTitle(title, true);
      }
    }

    // إذا لم نجد نمط محدد، نأخذ أول جملة
    const firstSentence = description.split('.')[0].trim();
    return optimizeTitle(firstSentence, true);
  } else {
    // استخراج العنوان الإنجليزي من الوصف
    const patterns = [
      /serving bowl[^,.]*/,
      /plate[^,.]*/,
      /tray[^,.]*/,
      /bowl[^,.]*/,
      /cup[^,.]*/,
      /pot[^,.]*/
    ];

    for (const pattern of patterns) {
      const match = description.match(pattern);
      if (match) {
        let title = match[0].trim();
        // تحويل إلى Title Case
        title = title.replace(/\w\S*/g, (txt) =>
          txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
        );
        return optimizeTitle(title, false);
      }
    }

    // إذا لم نجد نمط محدد، نأخذ أول جملة
    const firstSentence = description.split('.')[0].trim();
    return optimizeTitle(firstSentence, false);
  }
}

// تحسين العنوان ليكون أقل من 60 حرف
function optimizeTitle(title, isArabic = false) {
  if (title.length <= 60) return title;

  // قطع العنوان مع الحفاظ على الكلمات
  const words = title.split(' ');
  let optimized = '';

  for (const word of words) {
    if ((optimized + ' ' + word).length <= 57) {
      optimized += (optimized ? ' ' : '') + word;
    } else {
      break;
    }
  }

  return optimized + '...';
}

// تحسين الوصف ليكون أقل من 200 حرف
function optimizeDescription(description, isArabic = false) {
  if (description.length <= 200) return description;
  
  // قطع الوصف مع الحفاظ على الجمل
  const sentences = description.split('. ');
  let optimized = '';
  
  for (const sentence of sentences) {
    const withSentence = optimized + (optimized ? '. ' : '') + sentence;
    if (withSentence.length <= 197) {
      optimized = withSentence;
    } else {
      break;
    }
  }
  
  return optimized + '...';
}

// إصلاح منتج واحد
function fixProduct(product) {
  // إصلاح الحجم في المواصفات
  const sizeSpec = product.specifications.find(spec => spec.nameEn === 'Size');
  if (sizeSpec) {
    const enhancedSize = extractEnhancedSize(product.description);
    if (enhancedSize) {
      sizeSpec.valueEn = enhancedSize.en;
      sizeSpec.valueAr = enhancedSize.ar;
    }
  }

  // إنشاء عناوين جديدة من الوصف
  product.title = extractTitleFromDescription(product.description, false);
  product.titleAr = extractTitleFromDescription(product.descriptionAr, true);

  // إنشاء مميزات فريدة
  const uniqueFeatures = generateUniqueFeatures(product.description, product.titleAr);
  product.features = uniqueFeatures.en;
  product.featuresAr = uniqueFeatures.ar;

  // تحسين الوصف
  product.description = optimizeDescription(product.description, false);
  product.descriptionAr = optimizeDescription(product.descriptionAr, true);

  // تعيين السعر الأصلي إلى 0.00
  product.originalPrice = 0.00;

  return product;
}

// الدالة الرئيسية
function main() {
  console.log('🔧 بدء إصلاح ملف المنتجات...\n');
  
  const products = loadProducts();
  if (!products) return;
  
  console.log('🔄 إصلاح المنتجات...');
  const fixedProducts = products.map((product, index) => {
    try {
      const fixed = fixProduct(product);
      if ((index + 1) % 10 === 0) {
        console.log(`   ✅ تم إصلاح ${index + 1} منتج`);
      }
      return fixed;
    } catch (error) {
      console.error(`❌ خطأ في إصلاح المنتج ${product.id}:`, error.message);
      return product;
    }
  });
  
  // حفظ النتيجة
  const outputPath = '../professional-products-fixed.json';
  fs.writeFileSync(outputPath, JSON.stringify(fixedProducts, null, 2), 'utf8');
  
  console.log(`\n✅ تم إصلاح جميع المنتجات وحفظها في: ${outputPath}`);
  
  // إحصائيات
  console.log('\n📊 إحصائيات الإصلاح:');
  console.log(`   - عدد المنتجات: ${fixedProducts.length}`);
  
  // فحص الأحرف
  const longTitles = fixedProducts.filter(p => p.title.length > 60 || p.titleAr.length > 60);
  const longDescriptions = fixedProducts.filter(p => p.description.length > 200 || p.descriptionAr.length > 200);
  
  console.log(`   - عناوين تتجاوز 60 حرف: ${longTitles.length}`);
  console.log(`   - أوصاف تتجاوز 200 حرف: ${longDescriptions.length}`);
  
  // عرض عينة
  console.log('\n🔍 عينة من المنتجات المُصلحة:');
  console.log('=' .repeat(80));
  fixedProducts.slice(0, 2).forEach((product, index) => {
    console.log(`\n📦 المنتج ${index + 1}:`);
    console.log(`   ID: ${product.id}`);
    console.log(`   العنوان (${product.titleAr.length} حرف): ${product.titleAr}`);
    console.log(`   الوصف (${product.descriptionAr.length} حرف): ${product.descriptionAr.substring(0, 100)}...`);
    console.log(`   المميزات: ${product.featuresAr.slice(0, 2).join(', ')}...`);
    
    const sizeSpec = product.specifications.find(s => s.nameEn === 'Size');
    if (sizeSpec) {
      console.log(`   الحجم: ${sizeSpec.valueAr}`);
    }
  });
}

// تشغيل السكربت
if (require.main === module) {
  main();
}

module.exports = { fixProduct, generateUniqueFeatures, extractEnhancedSize, optimizeTitle, optimizeDescription };
